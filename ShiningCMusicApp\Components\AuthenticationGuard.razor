@using ShiningCMusicApp.Services
@inject IUserSessionService UserSession
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

@if (IsLoading)
{
    <div class="d-flex justify-content-center align-items-center" style="height: 50vh;">
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Checking authentication...</p>
        </div>
    </div>
}
else if (IsAuthorized)
{
    @ChildContent
}

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public bool RequireAdmin { get; set; } = false;
    
    private bool IsLoading = true;
    private bool IsAuthorized = false;

    protected override async Task OnInitializedAsync()
    {
        await CheckAuthenticationAsync();
    }

    private async Task CheckAuthenticationAsync()
    {
        try
        {
            // Small delay to ensure any ongoing authentication processes complete
            await Task.Delay(100);

            // Check if user is authenticated
            if (!UserSession.IsAuthenticated)
            {
                await JSRuntime.InvokeVoidAsync("console.log", "AuthGuard: Not authenticated, redirecting to login");
                Navigation.NavigateTo("/login", true);
                return;
            }

            // Check role-based authorization if required
            if (RequireAdmin && !UserSession.IsAdministrator)
            {
                await JSRuntime.InvokeVoidAsync("console.log", "AuthGuard: Admin required but user is not admin, redirecting to lessons");
                Navigation.NavigateTo("/lessons", true);
                return;
            }

            // User is authorized
            IsAuthorized = true;
            IsLoading = false;
            StateHasChanged();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"AuthGuard error: {ex.Message}");
            Navigation.NavigateTo("/login", true);
        }
    }
}
