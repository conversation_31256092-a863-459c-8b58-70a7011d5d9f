@using ShiningCMusicApp.Services
@using Microsoft.AspNetCore.Components.Routing
@inherits RouteView
@inject IUserSessionService UserSession
@inject NavigationManager Navigation

@if (ShouldRender)
{
    @if (IsLoginPage)
    {
        @RenderPage()
    }
    else if (!UserSession.IsAuthenticated)
    {
        @* Redirect to login if not authenticated *@
        @code {
            Navigation.NavigateTo("/login");
        }
    }
    else if (HasRoleAccess)
    {
        @RenderPage()
    }
    else
    {
        <div class="container mt-5">
            <div class="text-center">
                <div class="alert alert-warning">
                    <h4>Access Denied</h4>
                    <p>You don't have permission to access this page.</p>
                    <button class="btn btn-secondary" @onclick="RedirectToHome">
                        <i class="fas fa-home"></i> Go to Home
                    </button>
                </div>
            </div>
        </div>
    }
}

@code {
    private bool ShouldRender => true;

    private bool IsLoginPage => RouteData.PageType == typeof(Pages.Login);

    private bool HasRoleAccess
    {
        get
        {
            var currentPath = Navigation.ToBaseRelativePath(Navigation.Uri);

            // Administrator can access everything
            if (UserSession.IsAdministrator)
                return true;

            // Tutor and Student can only access lessons page
            if (UserSession.IsTutor || UserSession.IsStudent)
            {
                return currentPath.StartsWith("lessons") || currentPath == "";
            }

            return false;
        }
    }

    private RenderFragment RenderPage() => builder =>
    {
        builder.OpenComponent(0, RouteData.PageType);
        foreach (var kvp in RouteData.RouteValues)
        {
            builder.AddAttribute(1, kvp.Key, kvp.Value);
        }
        builder.CloseComponent();
    };

    protected override void OnInitialized()
    {
        UserSession.OnAuthenticationStateChanged += HandleAuthenticationStateChanged;
    }

    private void HandleAuthenticationStateChanged()
    {
        InvokeAsync(StateHasChanged);
    }

    private void RedirectToHome()
    {
        if (UserSession.IsAdministrator)
        {
            Navigation.NavigateTo("/");
        }
        else if (UserSession.IsTutor || UserSession.IsStudent)
        {
            Navigation.NavigateTo("/lessons");
        }
        else
        {
            Navigation.NavigateTo("/login");
        }
    }

    public void Dispose()
    {
        UserSession.OnAuthenticationStateChanged -= HandleAuthenticationStateChanged;
    }
}
