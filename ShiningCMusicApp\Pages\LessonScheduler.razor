@page "/lessons"
@using ShiningCMusicCommon.Models
@using ShiningCMusicApp.Services
@using Syncfusion.Blazor.Schedule
@using Syncfusion.Blazor.Inputs
@inject ILessonApiService LessonApi
@inject IUserSessionService UserSession
@inject IJSRuntime JSRuntime
@inject NavigationManager Navigation

<PageTitle>Lesson Time Table</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">🎵 @GetPageTitle()</h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading lessons...</p>
                </div>
            }
            else
            {
                @if (tutors.Any())
                {
                    <div class="card mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="bi bi-palette"></i> Tutor Colors</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                @foreach (var tutor in tutors)
                                {
                                    <div class="col-md-4 col-lg-3 mb-3">
                                        <div class="d-flex align-items-center">
                                            <SfColorPicker Value="@(tutor.Color ?? "#6C757D")"
                                                          ShowButtons="true"
                                                          Mode="ColorPickerMode.Picker"
                                                          ModeSwitcher="false"
                                                          ValueChange="@((ColorPickerEventArgs args) => OnTutorColorChanged(tutor.TutorId, args.CurrentValue.Hex))"
                                                          CssClass="me-2">
                                            </SfColorPicker>
                                            <small class="text-muted">@tutor.TutorName</small>
                                        </div>
                                    </div>
                                }
                            </div>
                            <small class="text-warning">
                                <i class="bi bi-info-circle"></i>
                                Select a color and click Apply to update all lessons for that tutor.
                            </small>
                        </div>
                    </div>
                }

                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
@*                         <h5 class="mb-0">Schedule (@scheduleEvents.Count lessons loaded)</h5>
 *@
                        @if (scheduleEvents.Any())
                        {
                            <h5 class="mb-0">
                                Next lesson: @scheduleEvents.Where(e => e.StartTime > DateTime.Now).FirstOrDefault()?.Subject at @scheduleEvents.Where(e => e.StartTime > DateTime.Now).FirstOrDefault()?.StartTime.ToString("g")
                            </h5>
                        }
                        <div>
                            <button class="btn btn-primary btn-sm" @onclick="RefreshData">
                                <i class="fas fa-refresh"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <SfSchedule TValue="ScheduleEvent"
                                   @bind-SelectedDate="@selectedDate"
                                   Height="800px"
                                   @bind-CurrentView="@currentView"
                                   FirstDayOfWeek="1"
                                   StartHour="06:00"
                                   EndHour="22:30"
                                   @ref="scheduleRef">

                            <ScheduleEvents TValue="ScheduleEvent"
                                          OnActionBegin="OnActionBegin"
                                          ActionCompleted="OnActionComplete"
                                          EventRendered="OnEventRendered"></ScheduleEvents>

                            <ScheduleTimeScale Enable="true" Interval="30" SlotCount="1"></ScheduleTimeScale>

                            <ScheduleEventSettings DataSource="@scheduleEvents"
                                                 TValue="ScheduleEvent">
                                <Template>
                                    @{
                                        var eventData = context as ScheduleEvent;
                                    }
                                    <div class="template-wrap">
                                        <div class="subject">@(eventData?.StudentName)</div>
                                        @if (!string.IsNullOrEmpty(eventData?.TutorName))
                                        {
                                            <div class="tutor">
                                                <i class="bi bi-person-fill"></i>@(eventData.TutorName)
                                            </div>
                                        }
                                        @if (!string.IsNullOrEmpty(eventData?.SubjectName))
                                        {
                                            <div class="student">
                                                <i class="bi bi-book-fill"></i>@(eventData.SubjectName)
                                            </div>
                                        }
                                    </div>
                                </Template>
                            </ScheduleEventSettings>

                            <ScheduleTemplates>
                                <EditorTemplate Context="eventData">
                                    @{
                                        var scheduleEvent = eventData as ScheduleEvent ?? new ScheduleEvent();
                                        currentEditingEvent = scheduleEvent;
                                    }
                                    <div class="custom-event-editor">
                                        <div class="row">
                                            <div class="col-md-12 mb-3">
                                                <label class="form-label">Student</label>
                                                <select class="form-select" @bind:get="scheduleEvent.StudentId" @bind:set="OnStudentSelectionChanged">
                                                    <option value="0">Select Student</option>
                                                    @foreach (var student in students)
                                                    {
                                                        <option value="@student.StudentId">@student.StudentName</option>
                                                    }
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Subject</label>
                                                <select class="form-select" @bind="scheduleEvent.SubjectId" @bind:after="OnSubjectChanged">
                                                    <option value="0">Select Subject</option>
                                                    @foreach (var subject in subjects)
                                                    {
                                                        <option value="@subject.SubjectId">@subject.SubjectName</option>
                                                    }
                                                </select>
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Tutor</label>
                                                <select class="form-select" @bind="scheduleEvent.TutorId" @bind:after="OnTutorChanged">
                                                    <option value="0">Select Tutor</option>
                                                    @foreach (var tutor in tutors)
                                                    {
                                                        <option value="@tutor.TutorId">@tutor.TutorName</option>
                                                    }
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Location</label>
                                                <select class="form-select" @bind="scheduleEvent.LocationId" @bind:after="OnLocationChanged">
                                                    <option value="0">Select Location</option>
                                                    @foreach (var location in locations)
                                                    {
                                                        <option value="@location.LocationId">@location.LocationName</option>
                                                    }
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">Start Time</label>
                                                <input type="datetime-local" class="form-control" @bind="scheduleEvent.StartTime" />
                                            </div>
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">End Time</label>
                                                <input type="datetime-local" class="form-control" @bind="scheduleEvent.EndTime" />
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6 mb-3">
                                                <label class="form-label">All Day</label>
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" @bind="scheduleEvent.IsAllDay" />
                                                    <label class="form-check-label">All Day Event</label>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-12 mb-3">
                                                <label class="form-label">Description</label>
                                                <textarea class="form-control" rows="3" @bind="scheduleEvent.Description" placeholder="Enter lesson description"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </EditorTemplate>
                            </ScheduleTemplates>

                            
                            <ScheduleViews>
                                <ScheduleView Option="View.Day"></ScheduleView>
                                <ScheduleView Option="View.Week"></ScheduleView>
                                <ScheduleView Option="View.Month"></ScheduleView>
                            </ScheduleViews>

                            <ScheduleQuickInfoTemplates TemplateType="TemplateType.Event">
                                <ContentTemplate>
                                    @{
                                        var eventData = context as ScheduleEvent;
                                    }
                                    <div class="quick-info">
                                        <div class="event-title">@(eventData?.SubjectName)</div>
                                        <div class="event-details">
                                            <p>
                                                <i class="bi bi-clock"></i>
                                                @(eventData?.StartTime.ToString("MMM dd, yyyy h:mm tt")) - @(eventData?.EndTime.ToString("h:mm tt"))
                                            </p>
                                            @*
                                            @if (!string.IsNullOrEmpty(eventData?.SubjectName))
                                            {
                                                <p>
                                                    <i class="bi bi-book"></i>
                                                    <strong>Subject:</strong> @(eventData.SubjectName)
                                                </p>
                                            }
                                            *@
                                            @if (!string.IsNullOrEmpty(eventData?.TutorName))
                                            {
                                                <p>
                                                    <i class="bi bi-person-fill"></i>
                                                    <strong>Tutor:</strong> @(eventData.TutorName)
                                                </p>
                                            }

                                            @if (!string.IsNullOrEmpty(eventData?.Location))
                                            {
                                                <p>
                                                    <i class="bi bi-geo-alt"></i>
                                                    <strong>Location:</strong> @(eventData.Location)
                                                </p>
                                            }
                                            @if (!string.IsNullOrEmpty(eventData?.Description))
                                            {
                                                <p>
                                                    <i class="bi bi-card-text"></i>
                                                    <strong>Description:</strong> @(eventData.Description)
                                                </p>
                                            }
                                        </div>
                                    </div>
                                </ContentTemplate>
                            </ScheduleQuickInfoTemplates>

                        </SfSchedule>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private List<ScheduleEvent> scheduleEvents = new();
    private List<Tutor> tutors = new();
    private List<Student> students = new();
    private List<Subject> subjects = new();
    private List<Location> locations = new();
    private DateTime selectedDate = DateTime.Today;
    private View currentView = View.Week;
    private bool isLoading = true;
    private SfSchedule<ScheduleEvent>? scheduleRef;
    private ScheduleEvent? currentEditingEvent;

    protected override async Task OnInitializedAsync()
    {
        // Check authentication
        if (!UserSession.IsAuthenticated)
        {
            Navigation.NavigateTo("/login");
            return;
        }

        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        try
        {
            // Load data from API
            var lessonsTask = LessonApi.GetLessonsAsync();
            var tutorsTask = LessonApi.GetTutorsAsync();
            var studentsTask = LessonApi.GetStudentsAsync();
            var subjectsTask = LessonApi.GetSubjectsAsync();
            var locationsTask = LessonApi.GetLocationsAsync();

            await Task.WhenAll(lessonsTask, tutorsTask, studentsTask, subjectsTask, locationsTask);

            var allLessons = await lessonsTask;
            tutors = await tutorsTask;
            students = await studentsTask;
            subjects = await subjectsTask;
            locations = await locationsTask;

            // Filter lessons based on user role
            if (UserSession.IsTutor)
            {
                // Find tutor by login name
                var currentTutor = tutors.FirstOrDefault(t => t.LoginName == UserSession.CurrentUser?.LoginName);
                if (currentTutor != null)
                {
                    scheduleEvents = allLessons.Where(l => l.TutorId == currentTutor.TutorId).ToList();
                }
                else
                {
                    scheduleEvents = new List<ScheduleEvent>();
                }
            }
            else if (UserSession.IsStudent)
            {
                // Find student by login name
                var currentStudent = students.FirstOrDefault(s => s.LoginName == UserSession.CurrentUser?.LoginName);
                if (currentStudent != null)
                {
                    scheduleEvents = allLessons.Where(l => l.StudentId == currentStudent.StudentId).ToList();
                }
                else
                {
                    scheduleEvents = new List<ScheduleEvent>();
                }
            }
            else
            {
                // Administrator sees all lessons
                scheduleEvents = allLessons;
            }

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {scheduleEvents.Count} lessons, {tutors.Count} tutors, {students.Count} students, {subjects.Count} subjects, {locations.Count} locations");

            // Debug: Log the actual lesson data and ensure proper DateTime format
            foreach (var lesson in scheduleEvents)
            {
                await JSRuntime.InvokeVoidAsync("console.log", $"Lesson: {lesson.Subject}, Start: {lesson.StartTime}, End: {lesson.EndTime}");

                // Ensure dates are in local time and properly formatted
                if (lesson.StartTime.Kind == DateTimeKind.Utc)
                {
                    lesson.StartTime = lesson.StartTime.ToLocalTime();
                }
                if (lesson.EndTime.Kind == DateTimeKind.Utc)
                {
                    lesson.EndTime = lesson.EndTime.ToLocalTime();
                }

                // Ensure dates have DateTimeKind.Local
                lesson.StartTime = DateTime.SpecifyKind(lesson.StartTime, DateTimeKind.Local);
                lesson.EndTime = DateTime.SpecifyKind(lesson.EndTime, DateTimeKind.Local);

                // Assign color based on tutor from database
                if (lesson.TutorId > 0)
                {
                    var tutor = tutors.FirstOrDefault(t => t.TutorId == lesson.TutorId);
                    lesson.CategoryColor = tutor?.Color ?? "#6C757D";
                }
            }

            // Force UI update
            // StateHasChanged();
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task AddNewLesson()
    {
        await JSRuntime.InvokeVoidAsync("alert", "Add new lesson functionality will be implemented in the next step!");
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private List<ScheduleEvent> GetScheduleEvents()
    {
        return scheduleEvents ?? new List<ScheduleEvent>();
    }

    private void OnStudentSelectionChanged(int studentId)
    {
        // Set the student ID first
        if (currentEditingEvent != null)
        {
            currentEditingEvent.StudentId = studentId;

            // Auto-populate subject and tutor when student is selected
            if (studentId > 0)
            {
                var selectedStudent = students.FirstOrDefault(s => s.StudentId == studentId);
                if (selectedStudent != null)
                {
                    // Auto-populate subject if student has one assigned
                    if (selectedStudent.SubjectId.HasValue && selectedStudent.SubjectId > 0)
                    {
                        currentEditingEvent.SubjectId = selectedStudent.SubjectId.Value;
                        currentEditingEvent.SubjectName = subjects.FirstOrDefault(s => s.SubjectId == selectedStudent.SubjectId)?.SubjectName;
                    }

                    // Auto-populate tutor if student has one assigned
                    if (selectedStudent.TutorID.HasValue && selectedStudent.TutorID > 0)
                    {
                        currentEditingEvent.TutorId = selectedStudent.TutorID.Value;
                        currentEditingEvent.TutorName = tutors.FirstOrDefault(t => t.TutorId == selectedStudent.TutorID)?.TutorName;
                    }
                }
            }
        }
        StateHasChanged();
    }

    private void OnTutorChanged()
    {
        // This method will be called when tutor selection changes
        // Auto-assign color based on selected tutor
        // Note: This method is called in the context of the editor template
        // The actual color assignment will happen in the CRUD event handlers
        StateHasChanged();
    }

    private void OnSubjectChanged()
    {
        // Force UI update when subject changes
        StateHasChanged();
    }

    private void OnLocationChanged()
    {
        // Force UI update when location changes
        StateHasChanged();
    }

    private string GetTutorColor(int tutorId)
    {
        var tutor = tutors.FirstOrDefault(t => t.TutorId == tutorId);
        return tutor?.Color ?? "#6C757D"; // Default gray if not found
    }

    private string GetTutorColorByName(string tutorName)
    {
        var tutor = tutors.FirstOrDefault(t => t.TutorName == tutorName);
        return tutor?.Color ?? "#6C757D";
    }



    private async Task OnTutorColorChanged(int tutorId, string newColor)
    {
        // Update the tutor color in the database
        var success = await LessonApi.UpdateTutorColorAsync(tutorId, newColor);

        if (success)
        {
            // Update the local tutor object
            var tutor = tutors.FirstOrDefault(t => t.TutorId == tutorId);
            if (tutor != null)
            {
                tutor.Color = newColor;
            }

            // Update all existing events for this tutor
            foreach (var scheduleEvent in scheduleEvents.Where(e => e.TutorId == tutorId))
            {
                scheduleEvent.CategoryColor = newColor;
            }

            // Refresh the schedule to show updated colors
            if (scheduleRef != null)
            {
                await scheduleRef.RefreshAsync();
            }

            StateHasChanged();

            await JSRuntime.InvokeVoidAsync("alert", $"Color updated for all {tutor?.TutorName} lessons!");
        }
        else
        {
            await JSRuntime.InvokeVoidAsync("alert", "Failed to update tutor color. Please try again.");
        }
    }

    private void OnEventRendered(EventRenderedArgs<ScheduleEvent> args)
    {
        // Apply the tutor color and tooltip to the event
        if (args.Data != null)
        {
            args.CssClasses = new List<string> { "custom-event" };

            // Create tooltip content
            var tooltipText = $"Tutor: {args.Data.TutorName ?? "Not assigned"}\nStudent: {args.Data.StudentName ?? "Not assigned"}\nTime: {args.Data.StartTime:h:mm tt} - {args.Data.EndTime:h:mm tt}";

            if (!string.IsNullOrEmpty(args.Data.Location))
            {
                tooltipText += $"\nLocation: {args.Data.Location}";
            }

            if (!string.IsNullOrEmpty(args.Data.Description))
            {
                tooltipText += $"\nDescription: {args.Data.Description}";
            }

            // Set attributes including color and tooltip
            args.Attributes = new Dictionary<string, object>
            {
                { "title", tooltipText },
                { "data-bs-toggle", "tooltip" },
                { "data-bs-placement", "top" }
            };

            // Apply tutor color if available
            if (!string.IsNullOrEmpty(args.Data.CategoryColor))
            {
                args.Attributes["style"] = $"background-color: {args.Data.CategoryColor} !important; border-color: {args.Data.CategoryColor} !important;";
            }
        }
    }

    private async Task OnActionBegin(ActionEventArgs<ScheduleEvent> args)
    {
        try
        {
            switch (args.ActionType)
            {
                case ActionType.EventCreate:
                    await HandleCreateEvent(args);
                    break;
                case ActionType.EventChange:
                    await HandleUpdateEvent(args);
                    break;
                case ActionType.EventRemove:
                    await HandleDeleteEvent(args);
                    break;
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error in OnActionBegin: {ex.Message}");
            args.Cancel = true;
            await JSRuntime.InvokeVoidAsync("alert", $"Error: {ex.Message}");
        }
    }

    private async Task OnActionComplete(ActionEventArgs<ScheduleEvent> args)
    {
        if (args.ActionType == ActionType.EventCreate ||
            args.ActionType == ActionType.EventChange ||
            args.ActionType == ActionType.EventRemove)
        {
            // Refresh the data after successful CRUD operation
            await LoadData();
            await JSRuntime.InvokeVoidAsync("console.log", "Data refreshed after CRUD operation");
        }
    }

    private async Task HandleCreateEvent(ActionEventArgs<ScheduleEvent> args)
    {
        if (args.AddedRecords?.Any() == true)
        {
            foreach (var newEvent in args.AddedRecords)
            {
                // Ensure required fields are set
                if (newEvent.TutorId == 0 || newEvent.StudentId == 0)
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Please select both a Tutor and Student for the lesson.");
                    return;
                }

                // Validate subject is selected
                if (newEvent.SubjectId == 0)
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Please select a subject for the lesson.");
                    return;
                }

                // Validate location is selected
                if (newEvent.LocationId == 0)
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Please select a location for the lesson.");
                    return;
                }

                // Set TutorName and StudentName from the loaded data
                var tutor = tutors.FirstOrDefault(t => t.TutorId == newEvent.TutorId);
                var student = students.FirstOrDefault(s => s.StudentId == newEvent.StudentId);

                if (tutor != null) newEvent.TutorName = tutor.TutorName;
                if (student != null) newEvent.StudentName = student.StudentName;

                // Set color based on tutor if not already set
                if (string.IsNullOrEmpty(newEvent.CategoryColor))
                {
                    newEvent.CategoryColor = GetTutorColor(newEvent.TutorId);
                }

                // Call API to create the lesson
                var createdEvent = await LessonApi.CreateLessonAsync(newEvent);
                if (createdEvent != null)
                {
                    newEvent.Id = createdEvent.Id;
                    await JSRuntime.InvokeVoidAsync("console.log", $"Created lesson with ID: {createdEvent.Id}");
                    await JSRuntime.InvokeVoidAsync("alert", "Lesson created successfully!");
                }
                else
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to create lesson. Please try again.");
                    return;
                }
            }
        }
    }

    private async Task HandleUpdateEvent(ActionEventArgs<ScheduleEvent> args)
    {
        if (args.ChangedRecords?.Any() == true)
        {
            foreach (var updatedEvent in args.ChangedRecords)
            {
                // Ensure required fields are set
                if (updatedEvent.TutorId == 0 || updatedEvent.StudentId == 0)
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Please select both a Tutor and Student for the lesson.");
                    return;
                }

                // Validate subject is selected
                if (updatedEvent.SubjectId == 0)
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Please select a subject for the lesson.");
                    return;
                }

                // Validate location is selected
                if (updatedEvent.LocationId == 0)
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Please select a location for the lesson.");
                    return;
                }

                // Set TutorName and StudentName from the loaded data
                var tutor = tutors.FirstOrDefault(t => t.TutorId == updatedEvent.TutorId);
                var student = students.FirstOrDefault(s => s.StudentId == updatedEvent.StudentId);

                if (tutor != null) updatedEvent.TutorName = tutor.TutorName;
                if (student != null) updatedEvent.StudentName = student.StudentName;

                // Update color based on tutor if not manually set
                if (string.IsNullOrEmpty(updatedEvent.CategoryColor))
                {
                    updatedEvent.CategoryColor = GetTutorColor(updatedEvent.TutorId);
                }

                // Call API to update the lesson
                var success = await LessonApi.UpdateLessonAsync(updatedEvent);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("console.log", $"Updated lesson with ID: {updatedEvent.Id}");
                    await JSRuntime.InvokeVoidAsync("alert", "Lesson updated successfully!");
                }
                else
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to update lesson. Please try again.");
                    return;
                }
            }
        }
    }

    private async Task HandleDeleteEvent(ActionEventArgs<ScheduleEvent> args)
    {
        if (args.DeletedRecords?.Any() == true)
        {
            // Ask for confirmation before deleting
            var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", "Are you sure you want to delete this lesson?");
            if (!confirmed)
            {
                args.Cancel = true;
                return;
            }

            foreach (var deletedEvent in args.DeletedRecords)
            {
                // Call API to delete the lesson
                var success = await LessonApi.DeleteLessonAsync(deletedEvent.Id);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("console.log", $"Deleted lesson with ID: {deletedEvent.Id}");
                    await JSRuntime.InvokeVoidAsync("alert", "Lesson deleted successfully!");
                }
                else
                {
                    args.Cancel = true;
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to delete lesson. Please try again.");
                    return;
                }
            }
        }
    }

    private string GetPageTitle()
    {
        if (UserSession.IsTutor)
        {
            return "My Lessons - Tutor View";
        }
        else if (UserSession.IsStudent)
        {
            return "My Lessons - Student View";
        }
        else
        {
            return "Lesson Time Table - Admin View";
        }
    }
}
