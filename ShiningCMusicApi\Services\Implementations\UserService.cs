using Dapper;
using Microsoft.Data.SqlClient;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Implementations
{
    public class UserService : IUserService
    {
        private readonly string _connectionString;

        public UserService(IConfiguration configuration)
        {
            _connectionString = Environment.GetEnvironmentVariable("ConnectionStrings_MusicSchool")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
        }

        public async Task<IEnumerable<User>> GetUsersAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT u.*, ur.Description as RoleDescription 
                FROM Users u 
                LEFT JOIN UserRoles ur ON u.RoleId = ur.ID 
                ORDER BY u.UserName";
            
            return await connection.QueryAsync<User>(sql);
        }

        public async Task<User?> GetUserAsync(string loginName)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT u.*, ur.Description as RoleDescription 
                FROM Users u 
                LEFT JOIN UserRoles ur ON u.RoleId = ur.ID 
                WHERE u.LoginName = @LoginName";
            
            return await connection.QueryFirstOrDefaultAsync<User>(sql, new { LoginName = loginName });
        }

        public async Task<User> CreateUserAsync(User user)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Users (LoginName, UserName, Password, Note, RoleId)
                VALUES (@LoginName, @UserName, @Password, @Note, @RoleId);";

            await connection.ExecuteAsync(sql, new
            {
                user.LoginName,
                user.UserName,
                user.Password,
                user.Note,
                user.RoleId
            });

            return user;
        }

        public async Task<bool> UpdateUserAsync(string loginName, User user)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Users
                SET UserName = @UserName,
                    Password = @Password,
                    Note = @Note,
                    RoleId = @RoleId
                WHERE LoginName = @LoginName";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                LoginName = loginName,
                user.UserName,
                user.Password,
                user.Note,
                user.RoleId
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteUserAsync(string loginName)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "DELETE FROM Users WHERE LoginName = @LoginName";
            var rowsAffected = await connection.ExecuteAsync(sql, new { LoginName = loginName });

            return rowsAffected > 0;
        }

        public async Task<User?> AuthenticateAsync(string loginName, string password)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                SELECT u.*, ur.Description as RoleDescription 
                FROM Users u 
                LEFT JOIN UserRoles ur ON u.RoleId = ur.ID 
                WHERE u.LoginName = @LoginName AND u.Password = @Password";
            
            return await connection.QueryFirstOrDefaultAsync<User>(sql, new { LoginName = loginName, Password = password });
        }

        public async Task<IEnumerable<UserRole>> GetUserRolesAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT * FROM UserRoles ORDER BY Description";
            return await connection.QueryAsync<UserRole>(sql);
        }
    }
}
