@page "/admin"
@using ShiningCMusicCommon.Models
@using ShiningCMusicApp.Services
@using Syncfusion.Blazor.Grids
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using Syncfusion.Blazor.Popups
@inject ILessonApiService LessonApi
@inject IJSRuntime JSRuntime
@attribute [Authorize(Roles = "Administrator")]

<PageTitle>Admin Management</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">⚙️ Admin Management</h1>
            
            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading admin data...</p>
                </div>
            }
            else
            {
                <!-- Subjects Section -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">📚 Subjects</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" style="width: 200px;" @onclick="OpenCreateSubjectModal">
                                <i class="fas fa-plus"></i> Add New Student
                            </button>
                            <button class="btn btn-secondary" style="width: 200px;" @onclick="RefreshData">
                                <i class="fas fa-refresh"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <SfGrid DataSource="@subjects" AllowPaging="true" AllowSorting="true" AllowFiltering="true" 
                                AllowResizing="true" Height="300">
                            <GridPageSettings PageSize="5"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Subject.SubjectId) HeaderText="ID" Width="80" IsPrimaryKey="true" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(Subject.SubjectName) HeaderText="Subject Name" Width="200"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var subject = (context as Subject);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-primary" style="width: 50%;" @onclick="() => OpenEditSubjectModal(subject)"
                                                    title="Edit">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-outline-danger" style="width: 50%;" @onclick="() => DeleteSubject(subject)"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>

                <!-- Locations Section -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">📍 Locations</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" style="width: 200px;" @onclick="OpenCreateLocationModal">
                                <i class="fas fa-plus"></i> Add New Student
                            </button>
                            <button class="btn btn-secondary" style="width: 200px;" @onclick="RefreshData">
                                <i class="fas fa-refresh"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <SfGrid DataSource="@locations" AllowPaging="true" AllowSorting="true" AllowFiltering="true" 
                                AllowResizing="true" Height="300">
                            <GridPageSettings PageSize="5"></GridPageSettings>
                            <GridFilterSettings Type="Syncfusion.Blazor.Grids.FilterType.FilterBar"></GridFilterSettings>
                            <GridColumns>
                                <GridColumn Field=@nameof(Location.LocationId) HeaderText="ID" Width="80" IsPrimaryKey="true" 
                                           AllowFiltering="false"></GridColumn>
                                <GridColumn Field=@nameof(Location.LocationName) HeaderText="Location Name" Width="200"></GridColumn>
                                <GridColumn HeaderText="Actions" Width="150" AllowFiltering="false" AllowSorting="false">
                                    <Template>
                                        @{
                                            var location = (context as Location);
                                        }
                                        <div class="d-flex gap-1">
                                            <button class="btn btn-outline-primary" style="width: 50%;" @onclick="() => OpenEditLocationModal(location)"
                                                    title="Edit">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-outline-danger" style="width: 50%;" @onclick="() => DeleteLocation(location)"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </Template>
                                </GridColumn>
                            </GridColumns>
                        </SfGrid>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Subject Modal -->
<SfDialog @bind-Visible="showSubjectModal" Header="@subjectModalTitle" Width="400px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentSubject" OnValidSubmit="@SaveSubject">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Subject Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentSubject.SubjectName" Placeholder="Enter subject name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentSubject.SubjectName)" />
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-secondary" @onclick="CloseSubjectModal">Cancel</SfButton>
                    <SfButton CssClass="btn btn-primary" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        @(isEditSubjectMode ? "Update" : "Create")
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

<!-- Location Modal -->
<SfDialog @bind-Visible="showLocationModal" Header="@locationModalTitle" Width="400px" Height="auto" IsModal="true" 
          ShowCloseIcon="true" AllowDragging="true">
    <DialogTemplates>
        <Content>
            <EditForm Model="@currentLocation" OnValidSubmit="@SaveLocation">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-danger" />
                
                <div class="mb-3">
                    <label class="form-label">Location Name <span class="text-danger">*</span></label>
                    <SfTextBox @bind-Value="currentLocation.LocationName" Placeholder="Enter location name" 
                               CssClass="form-control"></SfTextBox>
                    <ValidationMessage For="@(() => currentLocation.LocationName)" />
                </div>

                <div class="d-flex justify-content-end gap-2 mt-4">
                    <SfButton CssClass="btn btn-secondary" @onclick="CloseLocationModal">Cancel</SfButton>
                    <SfButton CssClass="btn btn-primary" type="submit" Disabled="@isSaving">
                        @if (isSaving)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        }
                        @(isEditLocationMode ? "Update" : "Create")
                    </SfButton>
                </div>
            </EditForm>
        </Content>
    </DialogTemplates>
</SfDialog>

@code {
    private List<Subject> subjects = new();
    private List<Location> locations = new();
    private bool isLoading = true;
    
    // Subject modal variables
    private bool showSubjectModal = false;
    private bool isEditSubjectMode = false;
    private bool isSaving = false;
    private string subjectModalTitle = "";
    private Subject currentSubject = new();
    
    // Location modal variables
    private bool showLocationModal = false;
    private bool isEditLocationMode = false;
    private string locationModalTitle = "";
    private Location currentLocation = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        try
        {
            var subjectsTask = LessonApi.GetSubjectsAsync();
            var locationsTask = LessonApi.GetLocationsAsync();

            await Task.WhenAll(subjectsTask, locationsTask);

            subjects = await subjectsTask;
            locations = await locationsTask;

            await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {subjects.Count} subjects and {locations.Count} locations");
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error loading data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    // Subject CRUD Methods
    private void OpenCreateSubjectModal()
    {
        currentSubject = new Subject();
        isEditSubjectMode = false;
        subjectModalTitle = "Create New Subject";
        showSubjectModal = true;
    }

    private async Task RefreshData()
    {
        await LoadData();
    }

    private void OpenEditSubjectModal(Subject? subject)
    {
        if (subject != null)
        {
            currentSubject = new Subject
            {
                SubjectId = subject.SubjectId,
                SubjectName = subject.SubjectName
            };

            isEditSubjectMode = true;
            subjectModalTitle = "Edit Subject";
            showSubjectModal = true;
        }
    }

    private void CloseSubjectModal()
    {
        showSubjectModal = false;
        currentSubject = new();
        isSaving = false;
    }

    private async Task SaveSubject()
    {
        if (string.IsNullOrWhiteSpace(currentSubject.SubjectName))
        {
            await JSRuntime.InvokeVoidAsync("alert", "Subject name is required.");
            return;
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditSubjectMode)
            {
                success = await LessonApi.UpdateSubjectAsync(currentSubject.SubjectId, currentSubject);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Subject updated successfully!");
                }
            }
            else
            {
                var createdSubject = await LessonApi.CreateSubjectAsync(currentSubject);
                success = createdSubject != null;
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Subject created successfully!");
                }
            }

            if (success)
            {
                CloseSubjectModal();
                await LoadData();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Failed to save subject. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving subject: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving subject: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteSubject(Subject? subject)
    {
        if (subject == null) return;

        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm", 
            $"Are you sure you want to delete subject '{subject.SubjectName}'? This action cannot be undone.");
        
        if (confirmed)
        {
            try
            {
                var success = await LessonApi.DeleteSubjectAsync(subject.SubjectId);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Subject deleted successfully!");
                    await LoadData();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to delete subject. Please try again.");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting subject: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", $"Error deleting subject: {ex.Message}");
            }
        }
    }

    // Location CRUD Methods
    private void OpenCreateLocationModal()
    {
        currentLocation = new Location();
        isEditLocationMode = false;
        locationModalTitle = "Create New Location";
        showLocationModal = true;
    }

    private void OpenEditLocationModal(Location? location)
    {
        if (location != null)
        {
            currentLocation = new Location
            {
                LocationId = location.LocationId,
                LocationName = location.LocationName
            };

            isEditLocationMode = true;
            locationModalTitle = "Edit Location";
            showLocationModal = true;
        }
    }

    private void CloseLocationModal()
    {
        showLocationModal = false;
        currentLocation = new();
        isSaving = false;
    }

    private async Task SaveLocation()
    {
        if (string.IsNullOrWhiteSpace(currentLocation.LocationName))
        {
            await JSRuntime.InvokeVoidAsync("alert", "Location name is required.");
            return;
        }

        isSaving = true;
        try
        {
            bool success;
            if (isEditLocationMode)
            {
                success = await LessonApi.UpdateLocationAsync(currentLocation.LocationId, currentLocation);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Location updated successfully!");
                }
            }
            else
            {
                var createdLocation = await LessonApi.CreateLocationAsync(currentLocation);
                success = createdLocation != null;
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Location created successfully!");
                }
            }

            if (success)
            {
                CloseLocationModal();
                await LoadData();
            }
            else
            {
                await JSRuntime.InvokeVoidAsync("alert", "Failed to save location. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await JSRuntime.InvokeVoidAsync("console.error", $"Error saving location: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Error saving location: {ex.Message}");
        }
        finally
        {
            isSaving = false;
        }
    }

    private async Task DeleteLocation(Location? location)
    {
        if (location == null) return;

        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
            $"Are you sure you want to delete location '{location.LocationName}'? This action cannot be undone.");

        if (confirmed)
        {
            try
            {
                var success = await LessonApi.DeleteLocationAsync(location.LocationId);
                if (success)
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Location deleted successfully!");
                    await LoadData();
                }
                else
                {
                    await JSRuntime.InvokeVoidAsync("alert", "Failed to delete location. Please try again.");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting location: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", $"Error deleting location: {ex.Message}");
            }
        }
    }
}
