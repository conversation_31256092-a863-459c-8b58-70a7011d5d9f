using ShiningCMusicCommon.Models;

namespace ShiningCMusicApp.Services
{
    public interface IUserSessionService
    {
        User? CurrentUser { get; }
        bool IsAuthenticated { get; }
        bool IsAdministrator { get; }
        bool IsTutor { get; }
        bool IsStudent { get; }
        Task<bool> LoginAsync(string loginName, string password);
        Task LogoutAsync();
        event Action? OnAuthenticationStateChanged;
    }

    public class UserSessionService : IUserSessionService
    {
        private readonly ILessonApiService _lessonApi;
        private User? _currentUser;
        private bool _isInitialized = false;

        public UserSessionService(ILessonApiService lessonApi)
        {
            _lessonApi = lessonApi;
            // Initialize with no user - user must login
            _isInitialized = true;
        }

        public User? CurrentUser => _currentUser;

        public bool IsAuthenticated => _currentUser != null;

        public bool IsAdministrator => _currentUser?.RoleId == 1; // Administrator role

        public bool IsTutor => _currentUser?.RoleId == 2; // Tutor role

        public bool IsStudent => _currentUser?.RoleId == 3; // Student role

        public event Action? OnAuthenticationStateChanged;

        public async Task<bool> LoginAsync(string loginName, string password)
        {
            try
            {
                var user = await _lessonApi.AuthenticateAsync(loginName, password);
                if (user != null)
                {
                    _currentUser = user;
                    OnAuthenticationStateChanged?.Invoke();
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Login error: {ex.Message}");
                return false;
            }
        }

        public async Task LogoutAsync()
        {
            _currentUser = null;
            OnAuthenticationStateChanged?.Invoke();
            await Task.CompletedTask;
        }
    }
}
