@using ShiningCMusicApp.Services
@inject IUserSessionService UserSession
@inject NavigationManager Navigation
@inject CustomAuthenticationStateProvider AuthStateProvider
@implements IDisposable

<div class="top-row ps-3 navbar navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="">Shining C Music</a>
        <button title="Navigation menu" class="navbar-toggler" @onclick="ToggleNavMenu">
            <span class="navbar-toggler-icon"></span>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass nav-scrollable" @onclick="ToggleNavMenu">
    <nav class="flex-column">

        <AuthorizeView Roles="Administrator">
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                    <span class="bi bi-house-door-fill-nav-menu" aria-hidden="true"></span> Home
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="lessons">
                    <span class="bi bi-calendar-event-nav-menu" aria-hidden="true"></span> Lessons
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="tutors">
                    <span class="bi bi-person-fill-nav-menu" aria-hidden="true"></span> Tutors
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="students">
                    <span class="bi bi-people-fill-nav-menu" aria-hidden="true"></span> Students
                </NavLink>
            </div>
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="admin">
                    <span class="bi bi-gear-fill-nav-menu" aria-hidden="true"></span> Admin
                </NavLink>
            </div>
        </AuthorizeView>

        <AuthorizeView Roles="Tutor,Student">
            <div class="nav-item px-3">
                <NavLink class="nav-link" href="lessons">
                    <span class="bi bi-calendar-event-nav-menu" aria-hidden="true"></span> My Lessons
                </NavLink>
            </div>
        </AuthorizeView>
@*         <div class="nav-item px-3">
            <NavLink class="nav-link" href="scheduler">
                <span class="bi bi-plus-square-fill-nav-menu" aria-hidden="true"></span> Scheduler
            </NavLink>
        </div>
        <div class="nav-item px-3">
            <NavLink class="nav-link" href="weather">
                <span class="bi bi-list-nested-nav-menu" aria-hidden="true"></span> Weather
            </NavLink>
        </div> *@
    </nav>
</div>

@code {
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "collapse" : null;

    protected override void OnInitialized()
    {
        UserSession.OnAuthenticationStateChanged += StateHasChanged;
    }

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    private async Task Logout()
    {
        await AuthStateProvider.LogoutAsync();
        Navigation.NavigateTo("/login");
    }

    public void Dispose()
    {
        UserSession.OnAuthenticationStateChanged -= StateHasChanged;
    }
}
