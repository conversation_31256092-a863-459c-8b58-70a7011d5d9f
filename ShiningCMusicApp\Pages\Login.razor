@page "/login"
@using ShiningCMusicApp.Services
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using System.ComponentModel.DataAnnotations
@inject IUserSessionService UserSession
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>Login - Shining C Music School</PageTitle>

<div class="login-container">
    <div class="login-card">
        <div class="text-center mb-4">
            <h1 class="display-6">🎵 Shining C Music School</h1>
            <p class="text-muted">Please sign in to continue</p>
        </div>

        <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin">
            <DataAnnotationsValidator />
            
            <div class="mb-3">
                <label class="form-label">Login Name</label>
                <SfTextBox @bind-Value="loginModel.LoginName" 
                          Placeholder="Enter your login name" 
                          CssClass="form-control"
                          Enabled="@(!isLoggingIn)"></SfTextBox>
                <ValidationMessage For="@(() => loginModel.LoginName)" />
            </div>

            <div class="mb-3">
                <label class="form-label">Password</label>
                <SfTextBox @bind-Value="loginModel.Password" 
                          Type="InputType.Password"
                          Placeholder="Enter your password" 
                          CssClass="form-control"
                          Enabled="@(!isLoggingIn)"></SfTextBox>
                <ValidationMessage For="@(() => loginModel.Password)" />
            </div>

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i> @errorMessage
                </div>
            }

            <div class="d-grid">
                <SfButton CssClass="btn btn-primary btn-lg"
                         type="submit"
                         Disabled="@isLoggingIn">
                    @if (isLoggingIn)
                    {
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        <span>Signing in...</span>
                    }
                    else
                    {
                        <i class="fas fa-sign-in-alt me-2"></i>
                        <span>Sign In</span>
                    }
                </SfButton>
            </div>

            <!-- Debug: Direct login button -->
            <div class="d-grid mt-2">
                <button type="button" class="btn btn-outline-secondary btn-sm" @onclick="HandleLogin">
                    Debug: Direct Login
                </button>
            </div>
        </EditForm>

        <div class="mt-4 text-center">
            <small class="text-muted">
                <strong>Demo Accounts:</strong><br>
                Admin: admin / admin<br>
                Tutor: sue / password123<br>
                Student: (contact admin)
            </small>
        </div>
    </div>
</div>

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
    }

    .login-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        padding: 40px;
        width: 100%;
        max-width: 400px;
    }

    .login-card h1 {
        color: #333;
        margin-bottom: 10px;
    }

    .form-label {
        font-weight: 600;
        color: #555;
        margin-bottom: 8px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 12px;
        font-weight: 600;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    .alert-danger {
        border-radius: 8px;
        border: none;
        background-color: #f8d7da;
        color: #721c24;
    }
</style>

@code {
    private LoginModel loginModel = new();
    private bool isLoggingIn = false;
    private string errorMessage = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        // If user is already authenticated, redirect to home
        if (UserSession.IsAuthenticated)
        {
            Navigation.NavigateTo("/");
        }
    }

    private async Task HandleLogin()
    {
        Console.WriteLine("=== HandleLogin method called ===");
        isLoggingIn = true;
        errorMessage = string.Empty;

        try
        {
            Console.WriteLine($"Login.HandleLogin called with: {loginModel.LoginName}");
            var success = await AuthStateProvider.LoginAsync(loginModel.LoginName, loginModel.Password);
            Console.WriteLine($"Login result: {success}");

            if (success)
            {
                // Get the current user to determine role
                var user = await AuthStateProvider.GetCurrentUserAsync();

                // Redirect based on user role
                if (user?.RoleId == 1) // Administrator
                {
                    Navigation.NavigateTo("/", true);
                }
                else if (user?.RoleId == 2 || user?.RoleId == 3) // Tutor or Student
                {
                    Navigation.NavigateTo("/lessons", true);
                }
                else
                {
                    Navigation.NavigateTo("/", true);
                }
            }
            else
            {
                errorMessage = "Invalid login name or password. Please try again.";
            }
        }
        catch (Exception)
        {
            errorMessage = "An error occurred during login. Please try again.";
        }
        finally
        {
            isLoggingIn = false;
        }
    }

    public class LoginModel
    {
        [Required(ErrorMessage = "Login name is required")]
        public string LoginName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        public string Password { get; set; } = string.Empty;
    }
}
