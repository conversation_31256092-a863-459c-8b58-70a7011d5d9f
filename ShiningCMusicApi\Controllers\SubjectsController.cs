using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShiningCMusicApi.Services.Interfaces;
using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SubjectsController : ControllerBase
    {
        private readonly ISubjectService _subjectService;
        private readonly ILogger<SubjectsController> _logger;

        public SubjectsController(ISubjectService subjectService, ILogger<SubjectsController> logger)
        {
            _subjectService = subjectService;
            _logger = logger;
        }

        // GET: api/subjects
        [HttpGet]
        public async Task<ActionResult<IEnumerable<Subject>>> GetSubjects()
        {
            try
            {
                var subjects = await _lessonService.GetSubjectsAsync();
                return Ok(subjects);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subjects");
                return StatusCode(500, "Internal server error");
            }
        }

        // GET: api/subjects/{id}
        [HttpGet("{id}")]
        public async Task<ActionResult<Subject>> GetSubject(int id)
        {
            try
            {
                var subject = await _lessonService.GetSubjectAsync(id);
                if (subject == null)
                {
                    return NotFound(new { message = "Subject not found" });
                }
                return Ok(subject);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving subject");
                return StatusCode(500, "Internal server error");
            }
        }

        // POST: api/subjects
        [HttpPost]
        public async Task<ActionResult<Subject>> CreateSubject([FromBody] Subject subject)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(subject.SubjectName))
                {
                    return BadRequest(new { message = "Subject name is required" });
                }

                var createdSubject = await _lessonService.CreateSubjectAsync(subject);
                return CreatedAtAction(nameof(GetSubject), new { id = createdSubject.SubjectId }, createdSubject);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating subject");
                return StatusCode(500, "Internal server error");
            }
        }

        // PUT: api/subjects/{id}
        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateSubject(int id, [FromBody] Subject subject)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(subject.SubjectName))
                {
                    return BadRequest(new { message = "Subject name is required" });
                }

                var success = await _lessonService.UpdateSubjectAsync(id, subject);
                if (success)
                {
                    return Ok(new { message = "Subject updated successfully" });
                }
                return NotFound(new { message = "Subject not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating subject");
                return StatusCode(500, "Internal server error");
            }
        }

        // DELETE: api/subjects/{id}
        [HttpDelete("{id}")]
        public async Task<IActionResult> DeleteSubject(int id)
        {
            try
            {
                var success = await _lessonService.DeleteSubjectAsync(id);
                if (success)
                {
                    return Ok(new { message = "Subject deleted successfully" });
                }
                return NotFound(new { message = "Subject not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting subject");
                return StatusCode(500, "Internal server error");
            }
        }
    }
}
