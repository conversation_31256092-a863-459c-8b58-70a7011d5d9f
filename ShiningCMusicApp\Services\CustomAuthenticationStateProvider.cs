using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using ShiningCMusicCommon.Models;
using System.Security.Claims;
using System.Text.Json;

namespace ShiningCMusicApp.Services
{
    public class CustomAuthenticationStateProvider : AuthenticationStateProvider
    {
        private readonly IJSRuntime _jsRuntime;
        private readonly ILessonApiService _lessonApi;
        private User? _currentUser;
        private readonly string USER_KEY = "currentUser";

        public CustomAuthenticationStateProvider(IJSRuntime jsRuntime, ILessonApiService lessonApi)
        {
            _jsRuntime = jsRuntime;
            _lessonApi = lessonApi;
        }

        public override async Task<AuthenticationState> GetAuthenticationStateAsync()
        {
            try
            {
                // If we already have the user in memory, use it
                if (_currentUser != null)
                {
                    return CreateAuthenticationState(_currentUser);
                }

                // Try to get user from localStorage
                var userJson = await _jsRuntime.InvokeAsync<string>("localStorage.getItem", USER_KEY);

                if (string.IsNullOrEmpty(userJson) || userJson == "null")
                {
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }

                var user = JsonSerializer.Deserialize<User>(userJson);
                if (user == null)
                {
                    return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
                }

                _currentUser = user;
                return CreateAuthenticationState(user);
            }
            catch
            {
                return new AuthenticationState(new ClaimsPrincipal(new ClaimsIdentity()));
            }
        }

        private AuthenticationState CreateAuthenticationState(User user)
        {
            var identity = new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.Name, user.UserName ?? user.LoginName),
                new Claim(ClaimTypes.NameIdentifier, user.LoginName),
                new Claim("RoleId", user.RoleId?.ToString() ?? "0"),
                new Claim(ClaimTypes.Role, GetRoleName(user.RoleId))
            }, "custom");

            return new AuthenticationState(new ClaimsPrincipal(identity));
        }

        public async Task<bool> LoginAsync(string loginName, string password)
        {
            try
            {
                var user = await _lessonApi.AuthenticateAsync(loginName, password);
                if (user != null)
                {
                    _currentUser = user;

                    // Store user in localStorage
                    var userJson = JsonSerializer.Serialize(user);
                    await _jsRuntime.InvokeVoidAsync("localStorage.setItem", USER_KEY, userJson);

                    // Notify that authentication state has changed
                    NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                // Clear any existing authentication state on error
                await LogoutAsync();
                return false;
            }
        }

        public async Task LogoutAsync()
        {
            _currentUser = null;
            await _jsRuntime.InvokeVoidAsync("localStorage.removeItem", USER_KEY);
            NotifyAuthenticationStateChanged(GetAuthenticationStateAsync());
        }

        private string GetRoleName(int? roleId)
        {
            return roleId switch
            {
                1 => "Administrator",
                2 => "Tutor",
                3 => "Student",
                _ => "Unknown"
            };
        }

        public User? CurrentUser => _currentUser;
        public bool IsAuthenticated => _currentUser != null;
        public bool IsAdministrator => _currentUser?.RoleId == 1;
        public bool IsTutor => _currentUser?.RoleId == 2;
        public bool IsStudent => _currentUser?.RoleId == 3;
    }
}
