﻿@page "/"
@using ShiningCMusicApp.Services
@inject IUserSessionService UserSession
@inject NavigationManager Navigation

<PageTitle>Shining C Music School</PageTitle>

<div class="container mt-4">
    <div class="row">
        <div class="col-12 text-center">
            <h1 class="display-4">🎵 Shining C Music School</h1>
            <p class="lead">Welcome to our music lesson management system</p>
        </div>
    </div>

    <div class="row mt-5">
        <div class="col-md-6 offset-md-3">
            <div class="card">
                <div class="card-body text-center">
                    <h5 class="card-title">Lesson Time Table</h5>
                    <p class="card-text">View and manage music lesson schedules for tutors and students.</p>
                    <a href="/lessons" class="btn btn-primary btn-lg">
                        <i class="fas fa-calendar-alt"></i> Open Scheduler
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h6 class="card-title">👨‍🏫 Manage Tutors</h6>
                    <p class="card-text">Add, edit, and manage tutor information</p>
                    <a href="/tutors" class="btn btn-outline-primary">
                        <i class="fas fa-user"></i> Manage Tutors
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h6 class="card-title">👨‍🎓 Manage Students</h6>
                    <p class="card-text">Add, edit, and manage student information</p>
                    <a href="/students" class="btn btn-outline-primary">
                        <i class="fas fa-users"></i> Manage Students
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h6 class="card-title">⚙️ Admin Management</h6>
                    <p class="card-text">Manage system configuration including subjects and locations</p>
                    <a href="/admin" class="btn btn-outline-primary">
                        <i class="fas fa-cog"></i> Admin Panel
                    </a>
                </div>
            </div>
        </div>
@*         <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h6 class="card-title">📅 Schedule Lessons</h6>
                    <p class="card-text">Create and manage lesson appointments</p>
                </div>
            </div>
        </div> *@
    </div>

    <!-- Admin Section -->
@*     <div class="row mt-5">
        <div class="col-md-8 offset-md-2">
            <div class="card border-warning">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">⚙️ Admin Management</h5>
                </div>
                <div class="card-body">
                    <p class="card-text">Manage system configuration including subjects and locations.</p>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-grid">
                                <a href="/admin" class="btn btn-warning">
                                    <i class="fas fa-cog"></i> Admin Panel
                                </a>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>Features:</strong><br>
                                • Manage Subjects (Piano, Guitar, etc.)<br>
                                • Manage Locations (Room 1, Room 2, etc.)
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div> *@

</div>

@code {
    protected override void OnInitialized()
    {
        // Redirect non-administrators to lessons page
        if (UserSession.IsAuthenticated && !UserSession.IsAdministrator)
        {
            Navigation.NavigateTo("/lessons");
        }
    }
}
