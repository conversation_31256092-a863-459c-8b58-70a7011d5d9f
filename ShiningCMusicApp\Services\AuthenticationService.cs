using System.Text;
using System.Text.Json;

namespace ShiningCMusicApp.Services
{
    public interface IAuthenticationService
    {
        Task<string?> GetAccessTokenAsync();
    }

    public class AuthenticationService : IAuthenticationService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private string? _cachedToken;
        private DateTime _tokenExpiry = DateTime.MinValue;

        public AuthenticationService(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _configuration = configuration;
        }

        public async Task<string?> GetAccessTokenAsync()
        {
            // Check if we have a valid cached token
            if (!string.IsNullOrEmpty(_cachedToken) && DateTime.UtcNow < _tokenExpiry)
            {
                return _cachedToken;
            }

            try
            {
                // Request new token from IdentityServer
                var tokenRequest = new
                {
                    grant_type = "client_credentials",
                    client_id = "wasm_client",
                    client_secret = "AE6qbzhQ08kW",
                    scope = "ShiningCMusicApi"
                };

                var content = new FormUrlEncodedContent(new[]
                {
                    new KeyValuePair<string, string>("grant_type", tokenRequest.grant_type),
                    new KeyValuePair<string, string>("client_id", tokenRequest.client_id),
                    new KeyValuePair<string, string>("client_secret", tokenRequest.client_secret),
                    new KeyValuePair<string, string>("scope", tokenRequest.scope)
                });

                var response = await _httpClient.PostAsync("https://localhost:7268/connect/token", content);
                
                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (tokenResponse != null && !string.IsNullOrEmpty(tokenResponse.access_token))
                    {
                        _cachedToken = tokenResponse.access_token;
                        _tokenExpiry = DateTime.UtcNow.AddSeconds(tokenResponse.expires_in - 60); // Refresh 1 minute early
                        return _cachedToken;
                    }
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"Token request failed: {response.StatusCode} - {errorContent}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting access token: {ex.Message}");
            }

            return null;
        }

        private class TokenResponse
        {
            public string access_token { get; set; } = string.Empty;
            public string token_type { get; set; } = string.Empty;
            public int expires_in { get; set; }
            public string scope { get; set; } = string.Empty;
        }
    }
}
