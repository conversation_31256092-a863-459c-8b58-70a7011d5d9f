@using ShiningCMusicApp.Services
@inject IUserSessionService UserSession
@inject NavigationManager Navigation

@if (IsAuthorized)
{
    @ChildContent
}
else
{
    <div class="text-center mt-5">
        <div class="alert alert-warning">
            <h4>Access Denied</h4>
            <p>You don't have permission to access this page.</p>
            @if (!UserSession.IsAuthenticated)
            {
                <button class="btn btn-primary" @onclick="RedirectToLogin">
                    <i class="fas fa-sign-in-alt"></i> Login
                </button>
            }
            else
            {
                <button class="btn btn-secondary" @onclick="RedirectToHome">
                    <i class="fas fa-home"></i> Go to Home
                </button>
            }
        </div>
    </div>
}

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public bool RequireAuthentication { get; set; } = true;
    [Parameter] public bool RequireAdministrator { get; set; } = false;
    [Parameter] public bool RequireTutor { get; set; } = false;
    [Parameter] public bool RequireStudent { get; set; } = false;

    private bool IsAuthorized
    {
        get
        {
            // If no authentication required, allow access
            if (!RequireAuthentication)
                return true;

            // Check if user is authenticated
            if (!UserSession.IsAuthenticated)
                return false;

            // Check specific role requirements
            if (RequireAdministrator && !UserSession.IsAdministrator)
                return false;

            if (RequireTutor && !UserSession.IsTutor)
                return false;

            if (RequireStudent && !UserSession.IsStudent)
                return false;

            return true;
        }
    }

    protected override void OnInitialized()
    {
        UserSession.OnAuthenticationStateChanged += StateHasChanged;
    }

    public void Dispose()
    {
        UserSession.OnAuthenticationStateChanged -= StateHasChanged;
    }

    private void RedirectToLogin()
    {
        Navigation.NavigateTo("/login");
    }

    private void RedirectToHome()
    {
        if (UserSession.IsAdministrator)
        {
            Navigation.NavigateTo("/");
        }
        else if (UserSession.IsTutor || UserSession.IsStudent)
        {
            Navigation.NavigateTo("/lessons");
        }
        else
        {
            Navigation.NavigateTo("/login");
        }
    }
}
