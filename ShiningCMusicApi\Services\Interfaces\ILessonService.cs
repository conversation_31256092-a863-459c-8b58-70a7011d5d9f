using ShiningCMusicCommon.Models;

namespace ShiningCMusicApi.Services.Interfaces
{
    public interface ILessonService
    {
        Task<IEnumerable<ScheduleEvent>> GetLessonsAsync();
        Task<ScheduleEvent?> GetLessonAsync(int id);
        Task<ScheduleEvent> CreateLessonAsync(ScheduleEvent lesson);
        Task<bool> UpdateLessonAsync(int id, ScheduleEvent lesson);
        Task<bool> DeleteLessonAsync(int id);
    }
}
