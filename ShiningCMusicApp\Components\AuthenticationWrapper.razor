@using ShiningCMusicApp.Services
@inject IUserSessionService UserSession
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@implements IDisposable

@if (IsLoading)
{
    <div class="d-flex justify-content-center align-items-center" style="height: 100vh;">
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Checking authentication...</p>
        </div>
    </div>
}
else if (ShouldShowContent)
{
    @ChildContent
}

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }
    
    private bool IsLoading = true;
    private bool ShouldShowContent = false;

    protected override async Task OnInitializedAsync()
    {
        UserSession.OnAuthenticationStateChanged += HandleAuthenticationStateChanged;
        await CheckAuthenticationAndRedirect();
    }

    private async Task CheckAuthenticationAndRedirect()
    {
        await Task.Delay(100); // Small delay to ensure session is initialized

        var currentPath = Navigation.ToBaseRelativePath(Navigation.Uri).ToLower();

        await JSRuntime.InvokeVoidAsync("console.log", $"AuthWrapper: Path={currentPath}, IsAuth={UserSession.IsAuthenticated}, User={UserSession.CurrentUser?.UserName}, RoleId={UserSession.CurrentUser?.RoleId}");

        // If we're in the AuthenticationWrapper within MainLayout,
        // we should not allow access to login page and must check authentication

        // Check if user is authenticated
        if (!UserSession.IsAuthenticated)
        {
            await JSRuntime.InvokeVoidAsync("console.log", "AuthWrapper: Not authenticated, redirecting to login");
            Navigation.NavigateTo("/login", true);
            return;
        }

        // Handle root path redirection for authenticated users
        if (currentPath == "")
        {
            await JSRuntime.InvokeVoidAsync("console.log", "AuthWrapper: Root path, redirecting based on role");
            RedirectBasedOnRole();
            return;
        }

        // Check role-based access
        if (!HasRoleAccess(currentPath))
        {
            await JSRuntime.InvokeVoidAsync("console.log", $"AuthWrapper: No role access for path: {currentPath}");
            RedirectBasedOnRole();
            return;
        }

        await JSRuntime.InvokeVoidAsync("console.log", "AuthWrapper: Access granted, showing content");
        ShouldShowContent = true;
        IsLoading = false;
        StateHasChanged();
    }

    private bool HasRoleAccess(string path)
    {
        // Administrator can access everything
        if (UserSession.IsAdministrator)
            return true;

        // Tutor and Student can only access lessons page
        if (UserSession.IsTutor || UserSession.IsStudent)
        {
            return path.StartsWith("lessons");
        }

        return false;
    }

    private void RedirectBasedOnRole()
    {
        if (UserSession.IsAdministrator)
        {
            Navigation.NavigateTo("/home", true);
        }
        else if (UserSession.IsTutor || UserSession.IsStudent)
        {
            Navigation.NavigateTo("/lessons", true);
        }
        else
        {
            Navigation.NavigateTo("/login", true);
        }
    }

    private async void HandleAuthenticationStateChanged()
    {
        IsLoading = true;
        ShouldShowContent = false;
        await InvokeAsync(StateHasChanged);
        await CheckAuthenticationAndRedirect();
    }

    public void Dispose()
    {
        UserSession.OnAuthenticationStateChanged -= HandleAuthenticationStateChanged;
    }
}
