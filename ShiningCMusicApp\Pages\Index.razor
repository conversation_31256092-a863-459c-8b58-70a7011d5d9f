@page "/"
@using ShiningCMusicApp.Services
@inject CustomAuthenticationStateProvider AuthStateProvider
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime

<PageTitle>Redirecting...</PageTitle>

<div class="d-flex justify-content-center align-items-center" style="height: 100vh;">
    <div class="text-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Redirecting...</p>
    </div>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Small delay to ensure authentication state is loaded
            await Task.Delay(100);
            
            // Get current authentication state
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                // User is authenticated, redirect based on role
                var user = await AuthStateProvider.GetCurrentUserAsync();
                
                if (user?.RoleId == 1) // Administrator
                {
                    Navigation.NavigateTo("/home", true);
                }
                else if (user?.RoleId == 2 || user?.RoleId == 3) // Tutor or Student
                {
                    Navigation.NavigateTo("/lessons", true);
                }
                else
                {
                    // Unknown role, redirect to login
                    Navigation.NavigateTo("/login", true);
                }
            }
            else
            {
                // User is not authenticated, redirect to login
                Navigation.NavigateTo("/login", true);
            }
        }
        catch (Exception ex)
        {
            // On any error, redirect to login
            await JSRuntime.InvokeVoidAsync("console.error", $"Root redirect error: {ex.Message}");
            Navigation.NavigateTo("/login", true);
        }
    }
}
