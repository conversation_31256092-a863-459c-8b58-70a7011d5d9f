using Dapper;
using System.Data.SqlClient;
using ShiningCMusicCommon.Models;
using ShiningCMusicApi.Services.Interfaces;

namespace ShiningCMusicApi.Services.Implementations
{
    public class LocationService : ILocationService
    {
        private readonly string _connectionString;

        public LocationService(IConfiguration configuration)
        {
            _connectionString = Environment.GetEnvironmentVariable("ConnectionStrings_MusicSchool")
                ?? configuration.GetConnectionString("MusicSchool")
                ?? throw new InvalidOperationException("Database connection string is missing.");
        }

        public async Task<IEnumerable<Location>> GetLocationsAsync()
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT LocationId, Location as LocationName FROM Locations ORDER BY Location";
            return await connection.QueryAsync<Location>(sql);
        }

        public async Task<Location?> GetLocationAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = "SELECT LocationId, Location as LocationName FROM Locations WHERE LocationId = @Id";
            return await connection.QueryFirstOrDefaultAsync<Location>(sql, new { Id = id });
        }

        public async Task<Location> CreateLocationAsync(Location location)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                INSERT INTO Locations (Location)
                VALUES (@LocationName);

                SELECT SCOPE_IDENTITY();";

            var newId = await connection.QuerySingleAsync<int>(sql, new
            {
                location.LocationName
            });

            location.LocationId = newId;
            return location;
        }

        public async Task<bool> UpdateLocationAsync(int id, Location location)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                UPDATE Locations
                SET Location = @LocationName
                WHERE LocationId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                location.LocationName
            });

            return rowsAffected > 0;
        }

        public async Task<bool> DeleteLocationAsync(int id)
        {
            using var connection = new SqlConnection(_connectionString);

            var sql = @"
                DELETE FROM Locations
                WHERE LocationId = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
            return rowsAffected > 0;
        }
    }
}
